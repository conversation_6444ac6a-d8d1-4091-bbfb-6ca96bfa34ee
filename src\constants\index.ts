import { ImageFormat, QualitySettings, AppConfig } from '../types';

// Default quality settings for each format
export const DEFAULT_QUALITY: QualitySettings = {
  jpeg: 0.92,
  webp: 0.90,
  png: 1.0,
  gif: 1.0,
  bmp: 1.0
};

// File size limits (in bytes)
export const FILE_SIZE_LIMITS = {
  MAX_FILE_SIZE: 50 * 1024 * 1024, // 50MB
  MAX_BATCH_SIZE: 20, // 20 files
  CHUNK_SIZE: 1024 * 1024, // 1MB chunks for processing
} as const;

// Supported MIME types
export const SUPPORTED_MIME_TYPES = [
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/webp',
  'image/gif',
  'image/bmp'
] as const;

// File extensions mapping
export const FILE_EXTENSIONS: Record<ImageFormat, string> = {
  [ImageFormat.JPEG]: 'jpg',
  [ImageFormat.PNG]: 'png',
  [ImageFormat.WEBP]: 'webp',
  [ImageFormat.GIF]: 'gif',
  [ImageFormat.BMP]: 'bmp'
};

// Format display names
export const FORMAT_NAMES: Record<ImageFormat, string> = {
  [ImageFormat.JPEG]: 'JPEG',
  [ImageFormat.PNG]: 'PNG',
  [ImageFormat.WEBP]: 'WebP',
  [ImageFormat.GIF]: 'GIF',
  [ImageFormat.BMP]: 'BMP'
};

// Application configuration
export const APP_CONFIG: AppConfig = {
  maxFileSize: FILE_SIZE_LIMITS.MAX_FILE_SIZE,
  maxBatchSize: FILE_SIZE_LIMITS.MAX_BATCH_SIZE,
  supportedFormats: Object.values(ImageFormat),
  defaultQuality: DEFAULT_QUALITY,
  chunkSize: FILE_SIZE_LIMITS.CHUNK_SIZE,
  workerPoolSize: navigator.hardwareConcurrency || 4
};

// Error messages
export const ERROR_MESSAGES = {
  UNSUPPORTED_FORMAT: 'This file format is not supported',
  FILE_TOO_LARGE: 'File size exceeds the maximum limit of 50MB',
  INVALID_FILE: 'Invalid or corrupted file',
  PROCESSING_ERROR: 'An error occurred while processing the image',
  MEMORY_ERROR: 'Insufficient memory to process this image',
  NETWORK_ERROR: 'Network error occurred',
  BATCH_TOO_LARGE: 'Too many files selected. Maximum is 20 files',
  NO_FILES_SELECTED: 'Please select at least one file to convert'
} as const;

// UI Constants
export const UI_CONSTANTS = {
  ANIMATION_DURATION: 300,
  DEBOUNCE_DELAY: 300,
  PROGRESS_UPDATE_INTERVAL: 100,
  PREVIEW_MAX_WIDTH: 300,
  PREVIEW_MAX_HEIGHT: 300,
  TOAST_DURATION: 5000
} as const;

// Canvas settings
export const CANVAS_SETTINGS = {
  MAX_CANVAS_SIZE: 32767, // Maximum canvas size in most browsers
  DEFAULT_SMOOTHING: true,
  DEFAULT_ALPHA: true,
  DEFAULT_COLOR_SPACE: 'srgb' as const
};

// Worker settings
export const WORKER_SETTINGS = {
  TIMEOUT: 30000, // 30 seconds
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000 // 1 second
};

// Download settings
export const DOWNLOAD_SETTINGS = {
  DEFAULT_ZIP_NAME: 'converted-images.zip',
  INDIVIDUAL_PREFIX: 'converted_',
  BATCH_FOLDER_NAME: 'converted-images'
};

// Validation patterns
export const VALIDATION_PATTERNS = {
  FILE_NAME: /^[a-zA-Z0-9._-]+$/,
  SAFE_FILE_NAME: /^[a-zA-Z0-9._\-\s()]+$/
};

// Performance thresholds
export const PERFORMANCE_THRESHOLDS = {
  LARGE_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  HIGH_RESOLUTION: 4096 * 4096, // 4K resolution
  BATCH_WARNING_SIZE: 10 // Warn when batch size exceeds this
};
