import './index.css';
import { ImageConverter } from './components/ImageConverter';
import { getErrorHandler } from './utils/errorHandler';

// Initialize theme
function initializeTheme(): void {
  const savedTheme = localStorage.getItem('theme');
  const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

  if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {
    document.documentElement.classList.add('dark');
  }
}

// Initialize performance monitoring
function initializePerformanceMonitoring(): void {
  // Monitor memory usage
  if ('memory' in performance) {
    const memoryInfo = (performance as any).memory;
    console.log('Initial memory usage:', {
      used: Math.round(memoryInfo.usedJSHeapSize / 1024 / 1024) + ' MB',
      total: Math.round(memoryInfo.totalJSHeapSize / 1024 / 1024) + ' MB',
      limit: Math.round(memoryInfo.jsHeapSizeLimit / 1024 / 1024) + ' MB'
    });
  }

  // Monitor performance
  if ('PerformanceObserver' in window) {
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'measure') {
          console.log(`Performance: ${entry.name} took ${entry.duration.toFixed(2)}ms`);
        }
      }
    });

    observer.observe({ entryTypes: ['measure'] });
  }
}

// Initialize service worker for caching (if available)
function initializeServiceWorker(): void {
  if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
      navigator.serviceWorker.register('/sw.js')
        .then((registration) => {
          console.log('SW registered: ', registration);
        })
        .catch((registrationError) => {
          console.log('SW registration failed: ', registrationError);
        });
    });
  }
}

// Main application initialization
async function initializeApp(): Promise<void> {
  try {
    // Initialize theme
    initializeTheme();

    // Initialize performance monitoring
    initializePerformanceMonitoring();

    // Initialize service worker
    initializeServiceWorker();

    // Create and initialize the image converter
    const imageConverter = new ImageConverter('app');

    // Make it globally accessible for HTML event handlers
    window.imageConverter = imageConverter;

    console.log('Image Converter initialized successfully');

  } catch (error) {
    getErrorHandler().handleCriticalError(error, 'Application initialization');
  }
}

// Start the application when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeApp);
} else {
  initializeApp();
}
