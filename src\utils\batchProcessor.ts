import {
  ImageFile,
  ConversionSettings,
  ConvertedFile,
  BatchJob,
  ProcessingProgress,
  ProcessingStatus,
  ProcessingError,
  ErrorType
} from '../types';
import { getWorkerManager } from './workerManager';
import { generateFileId } from './fileUtils';
import { APP_CONFIG, ERROR_MESSAGES } from '../constants';

export interface BatchProcessorOptions {
  onJobProgress?: (job: BatchJob) => void;
  onFileProgress?: (progress: ProcessingProgress) => void;
  onJobComplete?: (job: BatchJob) => void;
  onJobError?: (job: BatchJob, error: ProcessingError) => void;
  maxConcurrent?: number;
}

export class BatchProcessor {
  private workerManager = getWorkerManager();
  private activeJobs = new Map<string, BatchJob>();
  private options: BatchProcessorOptions;

  constructor(options: BatchProcessorOptions = {}) {
    this.options = {
      maxConcurrent: APP_CONFIG.workerPoolSize,
      ...options
    };
  }

  /**
   * Starts a new batch conversion job
   */
  async startBatchJob(
    files: ImageFile[],
    settings: ConversionSettings
  ): Promise<BatchJob> {
    // Validate batch
    this.validateBatch(files);

    // Create batch job
    const job: BatchJob = {
      id: generateFileId(),
      files,
      settings,
      progress: files.map(file => ({
        fileId: file.id,
        status: ProcessingStatus.PENDING,
        progress: 0,
        startTime: Date.now()
      })),
      results: [],
      startTime: Date.now(),
      totalFiles: files.length,
      completedFiles: 0,
      failedFiles: 0
    };

    this.activeJobs.set(job.id, job);

    // Start processing
    this.processBatch(job);

    return job;
  }

  /**
   * Validates batch before processing
   */
  private validateBatch(files: ImageFile[]): void {
    if (files.length === 0) {
      throw new Error(ERROR_MESSAGES.NO_FILES_SELECTED);
    }

    if (files.length > APP_CONFIG.maxBatchSize) {
      throw new Error(ERROR_MESSAGES.BATCH_TOO_LARGE);
    }

    // Check total memory usage estimate
    const totalMemoryEstimate = files.reduce((total, file) => {
      if (file.dimensions) {
        return total + (file.dimensions.width * file.dimensions.height * 4); // 4 bytes per pixel
      }
      return total + (1920 * 1080 * 4); // Assume HD if dimensions unknown
    }, 0);

    // Warn if memory usage might be high (>1GB)
    if (totalMemoryEstimate > 1024 * 1024 * 1024) {
      console.warn('High memory usage expected for this batch');
    }
  }

  /**
   * Processes a batch job
   */
  private async processBatch(job: BatchJob): Promise<void> {
    try {
      // Process files with concurrency control
      const promises = job.files.map((file, index) => 
        this.processFileWithRetry(job, file, index)
      );

      // Wait for all files to complete
      const results = await Promise.allSettled(promises);

      // Update job with results
      results.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          job.results.push(result.value);
          job.completedFiles++;
        } else {
          job.failedFiles++;
          this.updateFileProgress(job, index, {
            status: ProcessingStatus.ERROR,
            progress: 0,
            error: result.reason?.message || 'Unknown error'
          });
        }
      });

      // Finalize job
      job.endTime = Date.now();
      this.notifyJobComplete(job);

    } catch (error) {
      this.handleJobError(job, error);
    }
  }

  /**
   * Processes a single file with retry logic
   */
  private async processFileWithRetry(
    job: BatchJob,
    file: ImageFile,
    fileIndex: number,
    retryCount = 0
  ): Promise<ConvertedFile> {
    const maxRetries = 3;

    try {
      // Update progress to processing
      this.updateFileProgress(job, fileIndex, {
        status: ProcessingStatus.PROCESSING,
        progress: 0
      });

      // Convert image using worker manager
      const result = await this.workerManager.convertImage(
        file,
        job.settings,
        (progress) => this.handleFileProgress(job, fileIndex, progress)
      );

      // Update progress to completed
      this.updateFileProgress(job, fileIndex, {
        status: ProcessingStatus.COMPLETED,
        progress: 100
      });

      return result;

    } catch (error) {
      if (retryCount < maxRetries) {
        console.warn(`Retrying file ${file.name}, attempt ${retryCount + 1}`);
        await this.delay(1000 * (retryCount + 1)); // Exponential backoff
        return this.processFileWithRetry(job, file, fileIndex, retryCount + 1);
      }

      // Max retries reached
      this.updateFileProgress(job, fileIndex, {
        status: ProcessingStatus.ERROR,
        progress: 0,
        error: error.message || 'Processing failed'
      });

      throw error;
    }
  }

  /**
   * Handles progress updates for individual files
   */
  private handleFileProgress(
    job: BatchJob,
    fileIndex: number,
    progress: ProcessingProgress
  ): void {
    this.updateFileProgress(job, fileIndex, {
      status: progress.status,
      progress: progress.progress,
      message: progress.message
    });

    // Notify file progress callback
    if (this.options.onFileProgress) {
      this.options.onFileProgress(progress);
    }
  }

  /**
   * Updates progress for a specific file in the job
   */
  private updateFileProgress(
    job: BatchJob,
    fileIndex: number,
    updates: Partial<ProcessingProgress>
  ): void {
    if (fileIndex >= 0 && fileIndex < job.progress.length) {
      Object.assign(job.progress[fileIndex], updates);
      
      // Calculate overall job progress
      this.updateJobProgress(job);
    }
  }

  /**
   * Updates overall job progress
   */
  private updateJobProgress(job: BatchJob): void {
    const totalProgress = job.progress.reduce((sum, p) => sum + p.progress, 0);
    const averageProgress = totalProgress / job.progress.length;

    // Count completed and failed files
    job.completedFiles = job.progress.filter(p => p.status === ProcessingStatus.COMPLETED).length;
    job.failedFiles = job.progress.filter(p => p.status === ProcessingStatus.ERROR).length;

    // Notify job progress callback
    if (this.options.onJobProgress) {
      this.options.onJobProgress(job);
    }
  }

  /**
   * Handles job completion
   */
  private notifyJobComplete(job: BatchJob): void {
    if (this.options.onJobComplete) {
      this.options.onJobComplete(job);
    }
  }

  /**
   * Handles job errors
   */
  private handleJobError(job: BatchJob, error: any): void {
    job.endTime = Date.now();

    const processingError: ProcessingError = {
      type: ErrorType.PROCESSING_ERROR,
      message: error.message || ERROR_MESSAGES.PROCESSING_ERROR,
      details: error,
      timestamp: Date.now()
    };

    if (this.options.onJobError) {
      this.options.onJobError(job, processingError);
    }
  }

  /**
   * Gets job by ID
   */
  getJob(jobId: string): BatchJob | undefined {
    return this.activeJobs.get(jobId);
  }

  /**
   * Gets all active jobs
   */
  getActiveJobs(): BatchJob[] {
    return Array.from(this.activeJobs.values());
  }

  /**
   * Cancels a job
   */
  cancelJob(jobId: string): boolean {
    const job = this.activeJobs.get(jobId);
    if (!job) return false;

    // Update all pending/processing files to cancelled
    job.progress.forEach(progress => {
      if (progress.status === ProcessingStatus.PENDING || 
          progress.status === ProcessingStatus.PROCESSING) {
        progress.status = ProcessingStatus.CANCELLED;
        progress.endTime = Date.now();
      }
    });

    job.endTime = Date.now();
    this.activeJobs.delete(jobId);
    
    return true;
  }

  /**
   * Gets processing statistics
   */
  getStatistics(): {
    activeJobs: number;
    queueLength: number;
    activeTasks: number;
  } {
    return {
      activeJobs: this.activeJobs.size,
      queueLength: this.workerManager.getQueueLength(),
      activeTasks: this.workerManager.getActiveTasksCount()
    };
  }

  /**
   * Utility delay function
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    this.activeJobs.clear();
    // Note: WorkerManager is a singleton, so we don't destroy it here
  }
}

// Singleton instance
let batchProcessorInstance: BatchProcessor | null = null;

export function getBatchProcessor(options?: BatchProcessorOptions): BatchProcessor {
  if (!batchProcessorInstance) {
    batchProcessorInstance = new BatchProcessor(options);
  }
  return batchProcessorInstance;
}

/**
 * Utility function to estimate batch processing time
 */
export function estimateBatchTime(
  files: ImageFile[],
  avgProcessingTimePerMB: number = 2000 // 2 seconds per MB
): number {
  const totalSizeMB = files.reduce((total, file) => total + file.size, 0) / (1024 * 1024);
  const concurrency = Math.min(APP_CONFIG.workerPoolSize, files.length);
  
  return Math.ceil((totalSizeMB * avgProcessingTimePerMB) / concurrency);
}

/**
 * Utility function to optimize batch order
 */
export function optimizeBatchOrder(files: ImageFile[]): ImageFile[] {
  // Sort by file size (smaller files first for better perceived performance)
  return [...files].sort((a, b) => a.size - b.size);
}
