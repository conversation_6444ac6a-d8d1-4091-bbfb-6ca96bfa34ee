import { ProcessingError, ErrorType } from '../types';
import { ERROR_MESSAGES } from '../constants';
import { toastManager } from './uiUtils';

export class ErrorHandler {
  private errorLog: ProcessingError[] = [];
  private maxLogSize = 100;

  /**
   * Handles and logs an error
   */
  handleError(error: ProcessingError | Error | any, context?: string): ProcessingError {
    let processedError: ProcessingError;

    if (this.isProcessingError(error)) {
      processedError = error;
    } else {
      processedError = this.createProcessingError(error, context);
    }

    // Log the error
    this.logError(processedError);

    // Show user-friendly message
    this.showErrorToUser(processedError);

    return processedError;
  }

  /**
   * Creates a ProcessingError from various error types
   */
  private createProcessingError(error: any, context?: string): ProcessingError {
    let errorType = ErrorType.PROCESSING_ERROR;
    let message = ERROR_MESSAGES.PROCESSING_ERROR;

    // Determine error type based on error message or properties
    if (error.message) {
      const errorMessage = error.message.toLowerCase();
      
      if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
        errorType = ErrorType.NETWORK_ERROR;
        message = ERROR_MESSAGES.NETWORK_ERROR;
      } else if (errorMessage.includes('memory') || errorMessage.includes('out of memory')) {
        errorType = ErrorType.MEMORY_ERROR;
        message = ERROR_MESSAGES.MEMORY_ERROR;
      } else if (errorMessage.includes('format') || errorMessage.includes('unsupported')) {
        errorType = ErrorType.UNSUPPORTED_FORMAT;
        message = ERROR_MESSAGES.UNSUPPORTED_FORMAT;
      } else if (errorMessage.includes('size') || errorMessage.includes('large')) {
        errorType = ErrorType.FILE_TOO_LARGE;
        message = ERROR_MESSAGES.FILE_TOO_LARGE;
      } else if (errorMessage.includes('invalid') || errorMessage.includes('corrupt')) {
        errorType = ErrorType.INVALID_FILE;
        message = ERROR_MESSAGES.INVALID_FILE;
      }
    }

    return {
      type: errorType,
      message,
      details: error.message || error.toString(),
      timestamp: Date.now(),
      ...(context && { details: `${context}: ${error.message || error.toString()}` })
    };
  }

  /**
   * Checks if an object is a ProcessingError
   */
  private isProcessingError(error: any): error is ProcessingError {
    return error && 
           typeof error === 'object' && 
           'type' in error && 
           'message' in error && 
           'timestamp' in error;
  }

  /**
   * Logs an error to the internal log
   */
  private logError(error: ProcessingError): void {
    this.errorLog.unshift(error);
    
    // Keep log size manageable
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog = this.errorLog.slice(0, this.maxLogSize);
    }

    // Log to console for debugging
    console.error('Error logged:', error);
  }

  /**
   * Shows user-friendly error message
   */
  private showErrorToUser(error: ProcessingError): void {
    let displayMessage = error.message;
    
    // Add file context if available
    if (error.fileName) {
      displayMessage = `${error.fileName}: ${displayMessage}`;
    }

    // Show toast notification
    toastManager.show(displayMessage, 'error');
  }

  /**
   * Validates file before processing
   */
  validateFile(file: File): ProcessingError | null {
    // Check file size
    if (file.size === 0) {
      return {
        type: ErrorType.INVALID_FILE,
        message: 'File is empty',
        fileName: file.name,
        timestamp: Date.now()
      };
    }

    if (file.size > 50 * 1024 * 1024) { // 50MB limit
      return {
        type: ErrorType.FILE_TOO_LARGE,
        message: ERROR_MESSAGES.FILE_TOO_LARGE,
        fileName: file.name,
        timestamp: Date.now()
      };
    }

    // Check file type
    if (!file.type.startsWith('image/')) {
      return {
        type: ErrorType.UNSUPPORTED_FORMAT,
        message: ERROR_MESSAGES.UNSUPPORTED_FORMAT,
        fileName: file.name,
        timestamp: Date.now()
      };
    }

    return null;
  }

  /**
   * Validates batch of files
   */
  validateBatch(files: File[]): ProcessingError[] {
    const errors: ProcessingError[] = [];

    if (files.length === 0) {
      errors.push({
        type: ErrorType.INVALID_FILE,
        message: ERROR_MESSAGES.NO_FILES_SELECTED,
        timestamp: Date.now()
      });
      return errors;
    }

    if (files.length > 20) {
      errors.push({
        type: ErrorType.INVALID_FILE,
        message: ERROR_MESSAGES.BATCH_TOO_LARGE,
        timestamp: Date.now()
      });
      return errors;
    }

    // Validate each file
    files.forEach(file => {
      const fileError = this.validateFile(file);
      if (fileError) {
        errors.push(fileError);
      }
    });

    return errors;
  }

  /**
   * Handles network errors
   */
  handleNetworkError(error: any, operation: string): ProcessingError {
    const networkError: ProcessingError = {
      type: ErrorType.NETWORK_ERROR,
      message: `Network error during ${operation}`,
      details: error.message || error.toString(),
      timestamp: Date.now()
    };

    return this.handleError(networkError);
  }

  /**
   * Handles memory errors
   */
  handleMemoryError(error: any, fileName?: string): ProcessingError {
    const memoryError: ProcessingError = {
      type: ErrorType.MEMORY_ERROR,
      message: ERROR_MESSAGES.MEMORY_ERROR,
      fileName,
      details: error.message || error.toString(),
      timestamp: Date.now()
    };

    return this.handleError(memoryError);
  }

  /**
   * Handles processing errors with retry logic
   */
  handleProcessingError(
    error: any, 
    fileName?: string, 
    retryCount: number = 0,
    maxRetries: number = 3
  ): ProcessingError {
    const processingError: ProcessingError = {
      type: ErrorType.PROCESSING_ERROR,
      message: retryCount < maxRetries ? 
        `Processing failed, retrying... (${retryCount + 1}/${maxRetries})` : 
        ERROR_MESSAGES.PROCESSING_ERROR,
      fileName,
      details: error.message || error.toString(),
      timestamp: Date.now()
    };

    return this.handleError(processingError);
  }

  /**
   * Gets error statistics
   */
  getErrorStats(): {
    totalErrors: number;
    errorsByType: Record<ErrorType, number>;
    recentErrors: ProcessingError[];
  } {
    const errorsByType = Object.values(ErrorType).reduce((acc, type) => {
      acc[type] = this.errorLog.filter(error => error.type === type).length;
      return acc;
    }, {} as Record<ErrorType, number>);

    return {
      totalErrors: this.errorLog.length,
      errorsByType,
      recentErrors: this.errorLog.slice(0, 10)
    };
  }

  /**
   * Clears error log
   */
  clearErrorLog(): void {
    this.errorLog = [];
  }

  /**
   * Gets all errors for a specific file
   */
  getFileErrors(fileName: string): ProcessingError[] {
    return this.errorLog.filter(error => error.fileName === fileName);
  }

  /**
   * Exports error log for debugging
   */
  exportErrorLog(): string {
    return JSON.stringify(this.errorLog, null, 2);
  }

  /**
   * Creates error report for support
   */
  createErrorReport(): {
    timestamp: number;
    userAgent: string;
    errors: ProcessingError[];
    stats: ReturnType<typeof this.getErrorStats>;
  } {
    return {
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      errors: this.errorLog,
      stats: this.getErrorStats()
    };
  }

  /**
   * Handles critical errors that should stop the application
   */
  handleCriticalError(error: any, context: string): never {
    const criticalError: ProcessingError = {
      type: ErrorType.PROCESSING_ERROR,
      message: `Critical error in ${context}`,
      details: error.message || error.toString(),
      timestamp: Date.now()
    };

    this.logError(criticalError);
    
    // Show critical error to user
    toastManager.show(
      'A critical error occurred. Please refresh the page and try again.',
      'error',
      0 // Don't auto-dismiss
    );

    // Log to console
    console.error('CRITICAL ERROR:', criticalError);

    throw error;
  }

  /**
   * Wraps async functions with error handling
   */
  wrapAsync<T extends (...args: any[]) => Promise<any>>(
    fn: T,
    context: string
  ): T {
    return (async (...args: Parameters<T>) => {
      try {
        return await fn(...args);
      } catch (error) {
        return this.handleError(error, context);
      }
    }) as T;
  }

  /**
   * Wraps sync functions with error handling
   */
  wrapSync<T extends (...args: any[]) => any>(
    fn: T,
    context: string
  ): T {
    return ((...args: Parameters<T>) => {
      try {
        return fn(...args);
      } catch (error) {
        return this.handleError(error, context);
      }
    }) as T;
  }
}

// Singleton instance
let errorHandlerInstance: ErrorHandler | null = null;

export function getErrorHandler(): ErrorHandler {
  if (!errorHandlerInstance) {
    errorHandlerInstance = new ErrorHandler();
  }
  return errorHandlerInstance;
}

// Global error handlers
window.addEventListener('error', (event) => {
  getErrorHandler().handleError(event.error, 'Global error handler');
});

window.addEventListener('unhandledrejection', (event) => {
  getErrorHandler().handleError(event.reason, 'Unhandled promise rejection');
});

// Export convenience functions
export const handleError = (error: any, context?: string) => 
  getErrorHandler().handleError(error, context);

export const validateFile = (file: File) => 
  getErrorHandler().validateFile(file);

export const validateBatch = (files: File[]) => 
  getErrorHandler().validateBatch(files);
