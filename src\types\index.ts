// Supported image formats
export enum ImageFormat {
  JPEG = 'image/jpeg',
  PNG = 'image/png',
  WEBP = 'image/webp',
  GIF = 'image/gif',
  BMP = 'image/bmp'
}

// File processing status
export enum ProcessingStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  ERROR = 'error',
  CANCELLED = 'cancelled'
}

// Quality settings for different formats
export interface QualitySettings {
  jpeg: number; // 0.1 - 1.0
  webp: number; // 0.1 - 1.0
  png: number;  // Not applicable but kept for consistency
  gif: number;  // Not applicable but kept for consistency
  bmp: number;  // Not applicable but kept for consistency
}

// Image file information
export interface ImageFile {
  id: string;
  file: File;
  name: string;
  size: number;
  type: ImageFormat;
  dimensions?: {
    width: number;
    height: number;
  };
  preview?: string; // Base64 data URL
}

// Conversion settings
export interface ConversionSettings {
  targetFormat: ImageFormat;
  quality: number; // 0.1 - 1.0
  maintainDimensions: boolean;
  customDimensions?: {
    width: number;
    height: number;
  };
}

// Processing progress information
export interface ProcessingProgress {
  fileId: string;
  status: ProcessingStatus;
  progress: number; // 0 - 100
  message?: string;
  error?: string;
  startTime?: number;
  endTime?: number;
}

// Converted file result
export interface ConvertedFile {
  id: string;
  originalFile: ImageFile;
  convertedBlob: Blob;
  convertedName: string;
  convertedSize: number;
  convertedFormat: ImageFormat;
  conversionTime: number; // milliseconds
  compressionRatio: number; // original size / converted size
}

// Batch conversion job
export interface BatchJob {
  id: string;
  files: ImageFile[];
  settings: ConversionSettings;
  progress: ProcessingProgress[];
  results: ConvertedFile[];
  startTime: number;
  endTime?: number;
  totalFiles: number;
  completedFiles: number;
  failedFiles: number;
}

// Error types
export enum ErrorType {
  UNSUPPORTED_FORMAT = 'unsupported_format',
  FILE_TOO_LARGE = 'file_too_large',
  INVALID_FILE = 'invalid_file',
  PROCESSING_ERROR = 'processing_error',
  MEMORY_ERROR = 'memory_error',
  NETWORK_ERROR = 'network_error'
}

// Error information
export interface ProcessingError {
  type: ErrorType;
  message: string;
  fileId?: string;
  fileName?: string;
  details?: any;
  timestamp: number;
}

// Application configuration
export interface AppConfig {
  maxFileSize: number; // bytes
  maxBatchSize: number; // number of files
  supportedFormats: ImageFormat[];
  defaultQuality: QualitySettings;
  chunkSize: number; // for large file processing
  workerPoolSize: number;
}

// UI State
export interface UIState {
  isDragOver: boolean;
  isProcessing: boolean;
  currentJob?: BatchJob;
  errors: ProcessingError[];
  theme: 'light' | 'dark';
}

// Web Worker message types
export enum WorkerMessageType {
  CONVERT_IMAGE = 'convert_image',
  CONVERSION_PROGRESS = 'conversion_progress',
  CONVERSION_COMPLETE = 'conversion_complete',
  CONVERSION_ERROR = 'conversion_error'
}

// Web Worker messages
export interface WorkerMessage {
  type: WorkerMessageType;
  payload: any;
  id: string;
}

export interface ConvertImageMessage extends WorkerMessage {
  type: WorkerMessageType.CONVERT_IMAGE;
  payload: {
    imageData: ImageData;
    settings: ConversionSettings;
    fileName: string;
  };
}

export interface ConversionProgressMessage extends WorkerMessage {
  type: WorkerMessageType.CONVERSION_PROGRESS;
  payload: {
    progress: number;
    message?: string;
  };
}

export interface ConversionCompleteMessage extends WorkerMessage {
  type: WorkerMessageType.CONVERSION_COMPLETE;
  payload: {
    blob: Blob;
    fileName: string;
    conversionTime: number;
  };
}

export interface ConversionErrorMessage extends WorkerMessage {
  type: WorkerMessageType.CONVERSION_ERROR;
  payload: {
    error: ProcessingError;
  };
}

// File validation result
export interface ValidationResult {
  isValid: boolean;
  error?: ProcessingError;
}

// Download options
export interface DownloadOptions {
  individual: boolean;
  bulk: boolean;
  zipFileName?: string;
}

// Canvas processing options
export interface CanvasOptions {
  smoothing: boolean;
  alpha: boolean;
  colorSpace: 'srgb' | 'rec2020' | 'display-p3';
}
