import { 
  ImageFile, 
  ImageFormat, 
  ValidationResult, 
  ProcessingError, 
  ErrorType 
} from '../types';
import { 
  SUPPORTED_MIME_TYPES, 
  FILE_SIZE_LIMITS, 
  ERROR_MESSAGES,
  VALIDATION_PATTERNS 
} from '../constants';

/**
 * Validates a single file for image conversion
 */
export function validateFile(file: File): ValidationResult {
  // Check file type
  if (!SUPPORTED_MIME_TYPES.includes(file.type as any)) {
    return {
      isValid: false,
      error: {
        type: ErrorType.UNSUPPORTED_FORMAT,
        message: ERROR_MESSAGES.UNSUPPORTED_FORMAT,
        fileName: file.name,
        timestamp: Date.now()
      }
    };
  }

  // Check file size
  if (file.size > FILE_SIZE_LIMITS.MAX_FILE_SIZE) {
    return {
      isValid: false,
      error: {
        type: ErrorType.FILE_TOO_LARGE,
        message: ERROR_MESSAGES.FILE_TOO_LARGE,
        fileName: file.name,
        timestamp: Date.now()
      }
    };
  }

  // Check if file is empty
  if (file.size === 0) {
    return {
      isValid: false,
      error: {
        type: ErrorType.INVALID_FILE,
        message: ERROR_MESSAGES.INVALID_FILE,
        fileName: file.name,
        timestamp: Date.now()
      }
    };
  }

  return { isValid: true };
}

/**
 * Validates multiple files for batch processing
 */
export function validateFiles(files: File[]): {
  validFiles: File[];
  errors: ProcessingError[];
} {
  const validFiles: File[] = [];
  const errors: ProcessingError[] = [];

  // Check batch size
  if (files.length > FILE_SIZE_LIMITS.MAX_BATCH_SIZE) {
    errors.push({
      type: ErrorType.INVALID_FILE,
      message: ERROR_MESSAGES.BATCH_TOO_LARGE,
      timestamp: Date.now()
    });
    return { validFiles: [], errors };
  }

  // Validate each file
  files.forEach(file => {
    const validation = validateFile(file);
    if (validation.isValid) {
      validFiles.push(file);
    } else if (validation.error) {
      errors.push(validation.error);
    }
  });

  return { validFiles, errors };
}

/**
 * Converts File to ImageFile with metadata
 */
export async function fileToImageFile(file: File): Promise<ImageFile> {
  const id = generateFileId();
  const dimensions = await getImageDimensions(file);
  const preview = await generatePreview(file);

  return {
    id,
    file,
    name: file.name,
    size: file.size,
    type: file.type as ImageFormat,
    dimensions,
    preview
  };
}

/**
 * Gets image dimensions from file
 */
export function getImageDimensions(file: File): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    const url = URL.createObjectURL(file);

    img.onload = () => {
      URL.revokeObjectURL(url);
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight
      });
    };

    img.onerror = () => {
      URL.revokeObjectURL(url);
      reject(new Error('Failed to load image'));
    };

    img.src = url;
  });
}

/**
 * Generates a preview thumbnail for the image
 */
export function generatePreview(file: File, maxSize: number = 300): Promise<string> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();
    const url = URL.createObjectURL(file);

    if (!ctx) {
      reject(new Error('Canvas context not available'));
      return;
    }

    img.onload = () => {
      URL.revokeObjectURL(url);

      // Calculate thumbnail dimensions
      const { width, height } = calculateThumbnailSize(
        img.naturalWidth,
        img.naturalHeight,
        maxSize
      );

      canvas.width = width;
      canvas.height = height;

      // Draw thumbnail
      ctx.drawImage(img, 0, 0, width, height);

      // Convert to data URL
      resolve(canvas.toDataURL('image/jpeg', 0.8));
    };

    img.onerror = () => {
      URL.revokeObjectURL(url);
      reject(new Error('Failed to generate preview'));
    };

    img.src = url;
  });
}

/**
 * Calculates thumbnail dimensions maintaining aspect ratio
 */
export function calculateThumbnailSize(
  originalWidth: number,
  originalHeight: number,
  maxSize: number
): { width: number; height: number } {
  const aspectRatio = originalWidth / originalHeight;

  if (originalWidth > originalHeight) {
    return {
      width: Math.min(maxSize, originalWidth),
      height: Math.min(maxSize, originalWidth) / aspectRatio
    };
  } else {
    return {
      width: Math.min(maxSize, originalHeight) * aspectRatio,
      height: Math.min(maxSize, originalHeight)
    };
  }
}

/**
 * Generates unique file ID
 */
export function generateFileId(): string {
  return `file_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Formats file size for display
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Sanitizes filename for safe download
 */
export function sanitizeFileName(fileName: string): string {
  return fileName
    .replace(/[^a-zA-Z0-9._-]/g, '_')
    .replace(/_{2,}/g, '_')
    .replace(/^_|_$/g, '');
}

/**
 * Gets file extension from filename
 */
export function getFileExtension(fileName: string): string {
  const lastDot = fileName.lastIndexOf('.');
  return lastDot !== -1 ? fileName.substring(lastDot + 1).toLowerCase() : '';
}

/**
 * Changes file extension
 */
export function changeFileExtension(fileName: string, newExtension: string): string {
  const lastDot = fileName.lastIndexOf('.');
  const baseName = lastDot !== -1 ? fileName.substring(0, lastDot) : fileName;
  return `${baseName}.${newExtension}`;
}

/**
 * Checks if file is an image
 */
export function isImageFile(file: File): boolean {
  return file.type.startsWith('image/');
}

/**
 * Reads file as ArrayBuffer
 */
export function readFileAsArrayBuffer(file: File): Promise<ArrayBuffer> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as ArrayBuffer);
    reader.onerror = () => reject(reader.error);
    reader.readAsArrayBuffer(file);
  });
}

/**
 * Reads file as Data URL
 */
export function readFileAsDataURL(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = () => reject(reader.error);
    reader.readAsDataURL(file);
  });
}
