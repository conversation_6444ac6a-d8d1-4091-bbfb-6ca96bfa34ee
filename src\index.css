@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }
}

@layer components {
  /* Drop zone styles */
  .drop-zone {
    @apply border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center transition-all duration-300 cursor-pointer hover:border-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/20;
  }

  .drop-zone.drag-over {
    @apply border-primary-500 bg-primary-100 dark:bg-primary-900/30 scale-105;
  }

  /* File preview card */
  .file-card {
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 border border-gray-200 dark:border-gray-700 transition-all duration-300 hover:shadow-lg;
  }

  /* Progress bar */
  .progress-bar {
    @apply w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 overflow-hidden;
  }

  .progress-fill {
    @apply h-full bg-gradient-to-r from-primary-500 to-primary-600 transition-all duration-300 ease-out;
  }

  /* Button variants */
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;
  }

  .btn-danger {
    @apply bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2;
  }

  /* Status indicators */
  .status-pending {
    @apply text-yellow-600 dark:text-yellow-400;
  }

  .status-processing {
    @apply text-blue-600 dark:text-blue-400;
  }

  .status-completed {
    @apply text-green-600 dark:text-green-400;
  }

  .status-error {
    @apply text-red-600 dark:text-red-400;
  }

  /* Loading spinner */
  .spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
  }

  /* Toast notifications */
  .toast {
    @apply fixed top-4 right-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-4 max-w-sm z-50 transform transition-all duration-300;
  }

  .toast-success {
    @apply border-l-4 border-l-green-500;
  }

  .toast-error {
    @apply border-l-4 border-l-red-500;
  }

  .toast-warning {
    @apply border-l-4 border-l-yellow-500;
  }

  .toast-info {
    @apply border-l-4 border-l-blue-500;
  }
}

@layer utilities {
  /* Custom animations */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-pulse-slow {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  /* Scrollbar styling */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(156 163 175) transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgb(156 163 175);
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgb(107 114 128);
  }
}
