# Modern Image Converter

A high-performance, client-side image converter built with TypeScript, Vite, and Tailwind CSS. Convert between JPEG, PNG, WebP, GIF, and BMP formats with HD quality preservation and batch processing capabilities.

## 🚀 Features

### Core Functionality
- **Multi-format Support**: Convert between JPEG, PNG, WebP, GIF, and BMP formats
- **HD Quality Preservation**: Maintain original dimensions with 90-95% quality
- **Batch Processing**: Convert up to 20 files simultaneously
- **Client-side Processing**: No server uploads needed - all processing happens in your browser

### User Experience
- **Drag & Drop Interface**: Modern file upload with visual feedback
- **Real-time Preview**: Before/after image preview
- **Progress Indicators**: Track conversion progress for each file
- **Dark/Light Theme**: Automatic theme detection with manual toggle
- **Responsive Design**: Works on desktop and mobile devices

### Performance & Reliability
- **Web Workers**: Heavy processing doesn't block the UI
- **Memory Management**: Intelligent memory usage monitoring
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **Performance Monitoring**: Built-in performance metrics and optimization suggestions

### Download Options
- **Individual Downloads**: Download files one by one
- **Bulk ZIP Download**: Download all converted files as a ZIP archive
- **Smart Naming**: Automatic file naming with format extensions

## 🛠️ Technical Stack

- **Frontend**: Vanilla TypeScript with ES6+ modules
- **Build Tool**: Vite for fast development and bundling
- **Styling**: Tailwind CSS for responsive UI
- **File Processing**: HTML5 File API and Canvas API
- **Performance**: Web Workers for concurrent processing
- **Type Safety**: Strict TypeScript configuration

## 📦 Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd convert-project
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Start development server**:
   ```bash
   npm run dev
   ```

4. **Build for production**:
   ```bash
   npm run build
   ```

## 🎯 Usage

1. **Upload Files**: Drag and drop images or click to browse
2. **Select Format**: Choose target format (JPEG, PNG, WebP, GIF, BMP)
3. **Adjust Quality**: Set quality level for JPEG/WebP (10-100%)
4. **Convert**: Click "Convert Images" to start processing
5. **Download**: Download individual files or as ZIP archive

### Supported Formats

| Input Format | Output Formats | Quality Control |
|-------------|----------------|-----------------|
| JPEG        | All formats    | ✅ (10-100%)    |
| PNG         | All formats    | ❌ (Lossless)   |
| WebP        | All formats    | ✅ (10-100%)    |
| GIF         | All formats    | ❌ (Lossless)   |
| BMP         | All formats    | ❌ (Lossless)   |

### File Limitations

- **Maximum file size**: 50MB per file
- **Maximum batch size**: 20 files
- **Supported dimensions**: Up to 32,767 x 32,767 pixels

## 🏗️ Architecture

### Project Structure
```
src/
├── components/          # UI components
│   ├── FileUpload.ts   # Drag & drop functionality
│   └── ImageConverter.ts # Main application component
├── utils/              # Utility modules
│   ├── imageProcessor.ts    # Core image processing
│   ├── workerManager.ts     # Web worker management
│   ├── batchProcessor.ts    # Batch processing logic
│   ├── downloadManager.ts   # Download functionality
│   ├── errorHandler.ts      # Error handling
│   ├── fileUtils.ts         # File utilities
│   ├── uiUtils.ts          # UI utilities
│   └── performanceMonitor.ts # Performance monitoring
├── workers/            # Web workers
│   └── imageWorker.ts  # Image processing worker
├── types/              # TypeScript type definitions
│   └── index.ts
├── constants/          # Application constants
│   └── index.ts
└── test/              # Test files
    └── imageConverter.test.ts
```

### Key Components

1. **ImageConverter**: Main application component managing UI and orchestrating other components
2. **FileUpload**: Handles drag & drop file selection with validation
3. **ImageProcessor**: Core image conversion using Canvas API
4. **WorkerManager**: Manages Web Workers for concurrent processing
5. **BatchProcessor**: Handles batch conversion with progress tracking
6. **DownloadManager**: Manages file downloads (individual and ZIP)
7. **ErrorHandler**: Comprehensive error handling and user feedback
8. **PerformanceMonitor**: Monitors memory usage and processing performance

## 🔧 Configuration

### Quality Settings
```typescript
const DEFAULT_QUALITY = {
  jpeg: 0.92,  // 92% quality
  webp: 0.90,  // 90% quality
  png: 1.0,    // Lossless
  gif: 1.0,    // Lossless
  bmp: 1.0     // Lossless
};
```

### Performance Limits
```typescript
const LIMITS = {
  MAX_FILE_SIZE: 50 * 1024 * 1024,  // 50MB
  MAX_BATCH_SIZE: 20,               // 20 files
  MAX_CANVAS_SIZE: 32767,           // Browser limit
  MEMORY_THRESHOLD: 0.8             // 80% memory usage
};
```

## 🧪 Testing

Run the built-in tests:
```javascript
// In browser console
runImageConverterTests();
```

Tests cover:
- File validation
- Batch processing
- Format conversion
- Error handling
- Performance monitoring

## 🚀 Performance Optimizations

### Memory Management
- Automatic memory usage monitoring
- Intelligent batch size recommendations
- Canvas cleanup after processing
- Blob URL cleanup

### Processing Optimizations
- Web Workers prevent UI blocking
- Concurrent file processing
- Optimized canvas operations
- Progressive image loading

### User Experience
- Lazy loading for large images
- Debounced user interactions
- Progressive enhancement
- Offline capability with service worker

## 🔒 Security & Privacy

- **Client-side Only**: No files are uploaded to servers
- **Local Processing**: All conversion happens in your browser
- **No Data Collection**: No user data is collected or stored
- **Secure**: No external dependencies for core functionality

## 🌐 Browser Support

- **Chrome**: 80+
- **Firefox**: 75+
- **Safari**: 13+
- **Edge**: 80+

### Required APIs
- Canvas API
- File API
- Web Workers
- Blob API
- Download API

## 📝 License

This project is licensed under the ISC License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📞 Support

For issues and questions:
1. Check the browser console for error messages
2. Verify file formats and sizes are supported
3. Try processing smaller batches
4. Clear browser cache and reload

## 🔄 Changelog

### v1.0.0
- Initial release
- Multi-format image conversion
- Batch processing
- Web Worker implementation
- Performance monitoring
- Download management
