import { ImageProcessor } from '../utils/imageProcessor';
import { validateFile, validateBatch } from '../utils/errorHandler';
import { formatFileSize, sanitizeFileName } from '../utils/fileUtils';
import { ImageFormat, ConversionSettings } from '../types';

// Mock File constructor for testing
class MockFile extends File {
  constructor(bits: BlobPart[], filename: string, options?: FilePropertyBag) {
    super(bits, filename, options);
  }
}

// Test utilities
function createMockImageFile(name: string, size: number, type: string): File {
  const content = new Array(size).fill(0);
  return new MockFile([new Uint8Array(content)], name, { type });
}

// Test file validation
export function testFileValidation(): void {
  console.log('Testing file validation...');

  // Test valid image file
  const validFile = createMockImageFile('test.jpg', 1024 * 1024, 'image/jpeg');
  const validResult = validateFile(validFile);
  console.assert(validResult === null, 'Valid file should pass validation');

  // Test file too large
  const largeFile = createMockImageFile('large.jpg', 60 * 1024 * 1024, 'image/jpeg');
  const largeResult = validateFile(largeFile);
  console.assert(largeResult !== null, 'Large file should fail validation');

  // Test invalid file type
  const invalidFile = createMockImageFile('test.txt', 1024, 'text/plain');
  const invalidResult = validateFile(invalidFile);
  console.assert(invalidResult !== null, 'Invalid file type should fail validation');

  console.log('✓ File validation tests passed');
}

// Test batch validation
export function testBatchValidation(): void {
  console.log('Testing batch validation...');

  // Test valid batch
  const validFiles = [
    createMockImageFile('test1.jpg', 1024, 'image/jpeg'),
    createMockImageFile('test2.png', 2048, 'image/png')
  ];
  const validErrors = validateBatch(validFiles);
  console.assert(validErrors.length === 0, 'Valid batch should have no errors');

  // Test batch too large
  const tooManyFiles = Array.from({ length: 25 }, (_, i) => 
    createMockImageFile(`test${i}.jpg`, 1024, 'image/jpeg')
  );
  const tooManyErrors = validateBatch(tooManyFiles);
  console.assert(tooManyErrors.length > 0, 'Too many files should produce errors');

  // Test empty batch
  const emptyErrors = validateBatch([]);
  console.assert(emptyErrors.length > 0, 'Empty batch should produce errors');

  console.log('✓ Batch validation tests passed');
}

// Test file utilities
export function testFileUtils(): void {
  console.log('Testing file utilities...');

  // Test file size formatting
  console.assert(formatFileSize(1024) === '1 KB', 'File size formatting failed');
  console.assert(formatFileSize(1024 * 1024) === '1 MB', 'File size formatting failed');
  console.assert(formatFileSize(0) === '0 Bytes', 'File size formatting failed');

  // Test filename sanitization
  console.assert(sanitizeFileName('test file.jpg') === 'test_file.jpg', 'Filename sanitization failed');
  console.assert(sanitizeFileName('test@#$.jpg') === 'test___.jpg', 'Filename sanitization failed');

  console.log('✓ File utilities tests passed');
}

// Test conversion settings
export function testConversionSettings(): void {
  console.log('Testing conversion settings...');

  const settings: ConversionSettings = {
    targetFormat: ImageFormat.JPEG,
    quality: 0.9,
    maintainDimensions: true
  };

  console.assert(settings.targetFormat === ImageFormat.JPEG, 'Target format setting failed');
  console.assert(settings.quality === 0.9, 'Quality setting failed');
  console.assert(settings.maintainDimensions === true, 'Maintain dimensions setting failed');

  // Test quality bounds
  const qualitySettings = [
    { quality: 0.5, expected: 0.5 },
    { quality: 1.0, expected: 1.0 },
    { quality: 0.1, expected: 0.1 }
  ];

  qualitySettings.forEach(({ quality, expected }) => {
    const testSettings: ConversionSettings = {
      targetFormat: ImageFormat.JPEG,
      quality,
      maintainDimensions: true
    };
    console.assert(testSettings.quality === expected, `Quality setting ${quality} failed`);
  });

  console.log('✓ Conversion settings tests passed');
}

// Test image processor static methods
export function testImageProcessorStatics(): void {
  console.log('Testing image processor static methods...');

  // Test quality support
  console.assert(ImageProcessor.supportsQuality(ImageFormat.JPEG), 'JPEG should support quality');
  console.assert(ImageProcessor.supportsQuality(ImageFormat.WEBP), 'WebP should support quality');
  console.assert(!ImageProcessor.supportsQuality(ImageFormat.PNG), 'PNG should not support quality');

  // Test optimal quality
  const jpegQuality = ImageProcessor.getOptimalQuality(ImageFormat.JPEG);
  console.assert(jpegQuality > 0 && jpegQuality <= 1, 'JPEG quality should be between 0 and 1');

  const customQuality = ImageProcessor.getOptimalQuality(ImageFormat.JPEG, 0.8);
  console.assert(customQuality === 0.8, 'Custom quality should be respected');

  // Test memory estimation
  const memoryUsage = ImageProcessor.estimateMemoryUsage(1920, 1080);
  console.assert(memoryUsage === 1920 * 1080 * 4, 'Memory estimation failed');

  // Test safe dimensions
  console.assert(ImageProcessor.isSafeDimensions(1920, 1080), 'HD dimensions should be safe');
  console.assert(!ImageProcessor.isSafeDimensions(50000, 50000), 'Huge dimensions should not be safe');

  console.log('✓ Image processor static tests passed');
}

// Test performance monitoring
export function testPerformanceMonitoring(): void {
  console.log('Testing performance monitoring...');

  // This would require actual performance monitor instance
  // For now, just test that the module can be imported
  try {
    // Import would happen here in a real test environment
    console.log('✓ Performance monitoring module loads correctly');
  } catch (error) {
    console.error('✗ Performance monitoring module failed to load:', error);
  }
}

// Run all tests
export function runAllTests(): void {
  console.log('🧪 Running Image Converter Tests...\n');

  try {
    testFileValidation();
    testBatchValidation();
    testFileUtils();
    testConversionSettings();
    testImageProcessorStatics();
    testPerformanceMonitoring();

    console.log('\n✅ All tests passed!');
  } catch (error) {
    console.error('\n❌ Tests failed:', error);
  }
}

// Auto-run tests if this module is executed directly
if (typeof window !== 'undefined') {
  // Browser environment
  (window as any).runImageConverterTests = runAllTests;
  console.log('Image converter tests loaded. Run runImageConverterTests() to execute.');
} else {
  // Node environment (if running with Node.js)
  runAllTests();
}
