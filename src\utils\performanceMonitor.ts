export interface PerformanceMetrics {
  memoryUsage: {
    used: number;
    total: number;
    limit: number;
  };
  processingTimes: {
    [operation: string]: number[];
  };
  fileProcessingStats: {
    totalFiles: number;
    totalSize: number;
    averageProcessingTime: number;
    successRate: number;
  };
}

export class PerformanceMonitor {
  private metrics: PerformanceMetrics = {
    memoryUsage: { used: 0, total: 0, limit: 0 },
    processingTimes: {},
    fileProcessingStats: {
      totalFiles: 0,
      totalSize: 0,
      averageProcessingTime: 0,
      successRate: 100
    }
  };

  private activeOperations = new Map<string, number>();
  private memoryCheckInterval: number | null = null;

  constructor() {
    this.startMemoryMonitoring();
    this.setupPerformanceObserver();
  }

  /**
   * Starts monitoring memory usage
   */
  private startMemoryMonitoring(): void {
    this.updateMemoryUsage();
    
    // Check memory every 5 seconds
    this.memoryCheckInterval = setInterval(() => {
      this.updateMemoryUsage();
      this.checkMemoryThresholds();
    }, 5000);
  }

  /**
   * Updates current memory usage
   */
  private updateMemoryUsage(): void {
    if ('memory' in performance) {
      const memoryInfo = (performance as any).memory;
      this.metrics.memoryUsage = {
        used: memoryInfo.usedJSHeapSize,
        total: memoryInfo.totalJSHeapSize,
        limit: memoryInfo.jsHeapSizeLimit
      };
    }
  }

  /**
   * Checks memory usage thresholds and warns if necessary
   */
  private checkMemoryThresholds(): void {
    const { used, limit } = this.metrics.memoryUsage;
    const usagePercentage = (used / limit) * 100;

    if (usagePercentage > 80) {
      console.warn(`High memory usage: ${usagePercentage.toFixed(1)}%`);
      this.suggestMemoryOptimization();
    }
  }

  /**
   * Suggests memory optimization strategies
   */
  private suggestMemoryOptimization(): void {
    console.log('Memory optimization suggestions:');
    console.log('- Process files in smaller batches');
    console.log('- Clear completed results');
    console.log('- Reduce image quality settings');
  }

  /**
   * Sets up performance observer for timing measurements
   */
  private setupPerformanceObserver(): void {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'measure') {
            this.recordProcessingTime(entry.name, entry.duration);
          }
        }
      });
      
      observer.observe({ entryTypes: ['measure'] });
    }
  }

  /**
   * Starts timing an operation
   */
  startOperation(operationName: string): void {
    const markName = `${operationName}-start`;
    performance.mark(markName);
    this.activeOperations.set(operationName, Date.now());
  }

  /**
   * Ends timing an operation
   */
  endOperation(operationName: string): number {
    const startTime = this.activeOperations.get(operationName);
    if (!startTime) {
      console.warn(`Operation ${operationName} was not started`);
      return 0;
    }

    const endMarkName = `${operationName}-end`;
    const measureName = `${operationName}-duration`;
    
    performance.mark(endMarkName);
    performance.measure(measureName, `${operationName}-start`, endMarkName);
    
    const duration = Date.now() - startTime;
    this.activeOperations.delete(operationName);
    
    return duration;
  }

  /**
   * Records processing time for an operation
   */
  private recordProcessingTime(operation: string, duration: number): void {
    if (!this.metrics.processingTimes[operation]) {
      this.metrics.processingTimes[operation] = [];
    }
    
    this.metrics.processingTimes[operation].push(duration);
    
    // Keep only last 100 measurements
    if (this.metrics.processingTimes[operation].length > 100) {
      this.metrics.processingTimes[operation] = this.metrics.processingTimes[operation].slice(-100);
    }
  }

  /**
   * Records file processing statistics
   */
  recordFileProcessing(fileSize: number, processingTime: number, success: boolean): void {
    this.metrics.fileProcessingStats.totalFiles++;
    this.metrics.fileProcessingStats.totalSize += fileSize;
    
    // Update average processing time
    const totalTime = this.metrics.fileProcessingStats.averageProcessingTime * 
                     (this.metrics.fileProcessingStats.totalFiles - 1) + processingTime;
    this.metrics.fileProcessingStats.averageProcessingTime = 
      totalTime / this.metrics.fileProcessingStats.totalFiles;
    
    // Update success rate
    const successfulFiles = Math.round(
      this.metrics.fileProcessingStats.successRate * 
      (this.metrics.fileProcessingStats.totalFiles - 1) / 100
    ) + (success ? 1 : 0);
    
    this.metrics.fileProcessingStats.successRate = 
      (successfulFiles / this.metrics.fileProcessingStats.totalFiles) * 100;
  }

  /**
   * Gets current performance metrics
   */
  getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  /**
   * Gets formatted memory usage
   */
  getFormattedMemoryUsage(): string {
    const { used, total, limit } = this.metrics.memoryUsage;
    return `Used: ${this.formatBytes(used)} / Total: ${this.formatBytes(total)} / Limit: ${this.formatBytes(limit)}`;
  }

  /**
   * Gets average processing time for an operation
   */
  getAverageProcessingTime(operation: string): number {
    const times = this.metrics.processingTimes[operation];
    if (!times || times.length === 0) return 0;
    
    return times.reduce((sum, time) => sum + time, 0) / times.length;
  }

  /**
   * Gets processing time percentiles
   */
  getProcessingTimePercentiles(operation: string): { p50: number; p90: number; p95: number } {
    const times = this.metrics.processingTimes[operation];
    if (!times || times.length === 0) {
      return { p50: 0, p90: 0, p95: 0 };
    }
    
    const sorted = [...times].sort((a, b) => a - b);
    const len = sorted.length;
    
    return {
      p50: sorted[Math.floor(len * 0.5)] || 0,
      p90: sorted[Math.floor(len * 0.9)] || 0,
      p95: sorted[Math.floor(len * 0.95)] || 0
    };
  }

  /**
   * Estimates processing time for a file
   */
  estimateProcessingTime(fileSize: number): number {
    const { totalSize, totalFiles, averageProcessingTime } = this.metrics.fileProcessingStats;
    
    if (totalFiles === 0) {
      // Default estimate: 2ms per KB
      return (fileSize / 1024) * 2;
    }
    
    const averageFileSize = totalSize / totalFiles;
    const sizeRatio = fileSize / averageFileSize;
    
    return averageProcessingTime * sizeRatio;
  }

  /**
   * Checks if system can handle additional processing
   */
  canHandleAdditionalProcessing(estimatedMemoryUsage: number): boolean {
    const { used, limit } = this.metrics.memoryUsage;
    const projectedUsage = used + estimatedMemoryUsage;
    
    return (projectedUsage / limit) < 0.8; // Keep under 80% memory usage
  }

  /**
   * Gets performance recommendations
   */
  getRecommendations(): string[] {
    const recommendations: string[] = [];
    const { used, limit } = this.metrics.memoryUsage;
    const memoryUsagePercent = (used / limit) * 100;
    
    if (memoryUsagePercent > 70) {
      recommendations.push('Consider processing files in smaller batches');
    }
    
    if (this.metrics.fileProcessingStats.successRate < 95) {
      recommendations.push('Check for file format compatibility issues');
    }
    
    if (this.metrics.fileProcessingStats.averageProcessingTime > 5000) {
      recommendations.push('Consider reducing image quality settings for faster processing');
    }
    
    const activeOpsCount = this.activeOperations.size;
    if (activeOpsCount > 10) {
      recommendations.push('Too many concurrent operations, consider reducing batch size');
    }
    
    return recommendations;
  }

  /**
   * Formats bytes to human readable format
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Exports performance data for analysis
   */
  exportData(): string {
    return JSON.stringify({
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      metrics: this.metrics,
      recommendations: this.getRecommendations()
    }, null, 2);
  }

  /**
   * Resets all metrics
   */
  reset(): void {
    this.metrics = {
      memoryUsage: { used: 0, total: 0, limit: 0 },
      processingTimes: {},
      fileProcessingStats: {
        totalFiles: 0,
        totalSize: 0,
        averageProcessingTime: 0,
        successRate: 100
      }
    };
    this.activeOperations.clear();
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    if (this.memoryCheckInterval) {
      clearInterval(this.memoryCheckInterval);
      this.memoryCheckInterval = null;
    }
    this.activeOperations.clear();
  }
}

// Singleton instance
let performanceMonitorInstance: PerformanceMonitor | null = null;

export function getPerformanceMonitor(): PerformanceMonitor {
  if (!performanceMonitorInstance) {
    performanceMonitorInstance = new PerformanceMonitor();
  }
  return performanceMonitorInstance;
}
