import { ProcessingStatus } from '../types';
import { UI_CONSTANTS } from '../constants';

/**
 * Creates and manages toast notifications
 */
export class ToastManager {
  private container: HTMLElement;
  private toasts = new Map<string, HTMLElement>();

  constructor() {
    this.container = this.createContainer();
  }

  private createContainer(): HTMLElement {
    const container = document.createElement('div');
    container.id = 'toast-container';
    container.className = 'fixed top-4 right-4 z-50 space-y-2';
    document.body.appendChild(container);
    return container;
  }

  show(message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info', duration = UI_CONSTANTS.TOAST_DURATION): string {
    const id = `toast_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const toast = this.createToast(id, message, type);
    
    this.container.appendChild(toast);
    this.toasts.set(id, toast);

    // Animate in
    requestAnimationFrame(() => {
      toast.classList.add('animate-slide-up');
    });

    // Auto remove
    if (duration > 0) {
      setTimeout(() => this.remove(id), duration);
    }

    return id;
  }

  private createToast(id: string, message: string, type: string): HTMLElement {
    const toast = document.createElement('div');
    toast.id = id;
    toast.className = `toast toast-${type}`;

    const iconMap = {
      success: '✓',
      error: '✕',
      warning: '⚠',
      info: 'ℹ'
    };

    toast.innerHTML = `
      <div class="flex items-start">
        <div class="flex-shrink-0">
          <span class="text-lg">${iconMap[type as keyof typeof iconMap]}</span>
        </div>
        <div class="ml-3 flex-1">
          <p class="text-sm font-medium text-gray-900 dark:text-gray-100">${message}</p>
        </div>
        <div class="ml-4 flex-shrink-0">
          <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" onclick="window.toastManager.remove('${id}')">
            <span class="sr-only">Close</span>
            <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>
      </div>
    `;

    return toast;
  }

  remove(id: string): void {
    const toast = this.toasts.get(id);
    if (toast) {
      toast.style.transform = 'translateX(100%)';
      toast.style.opacity = '0';
      
      setTimeout(() => {
        if (toast.parentNode) {
          toast.parentNode.removeChild(toast);
        }
        this.toasts.delete(id);
      }, 300);
    }
  }

  clear(): void {
    this.toasts.forEach((_, id) => this.remove(id));
  }
}

/**
 * Creates a progress bar element
 */
export function createProgressBar(progress: number = 0): HTMLElement {
  const container = document.createElement('div');
  container.className = 'progress-bar';

  const fill = document.createElement('div');
  fill.className = 'progress-fill';
  fill.style.width = `${Math.max(0, Math.min(100, progress))}%`;

  container.appendChild(fill);
  return container;
}

/**
 * Updates a progress bar
 */
export function updateProgressBar(progressBar: HTMLElement, progress: number): void {
  const fill = progressBar.querySelector('.progress-fill') as HTMLElement;
  if (fill) {
    fill.style.width = `${Math.max(0, Math.min(100, progress))}%`;
  }
}

/**
 * Creates a loading spinner
 */
export function createSpinner(size: 'sm' | 'md' | 'lg' = 'md'): HTMLElement {
  const spinner = document.createElement('div');
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8'
  };
  
  spinner.className = `spinner ${sizeClasses[size]}`;
  return spinner;
}

/**
 * Creates a status badge
 */
export function createStatusBadge(status: ProcessingStatus): HTMLElement {
  const badge = document.createElement('span');
  badge.className = `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium status-${status}`;

  const statusText = {
    [ProcessingStatus.PENDING]: 'Pending',
    [ProcessingStatus.PROCESSING]: 'Processing',
    [ProcessingStatus.COMPLETED]: 'Completed',
    [ProcessingStatus.ERROR]: 'Error',
    [ProcessingStatus.CANCELLED]: 'Cancelled'
  };

  const statusIcons = {
    [ProcessingStatus.PENDING]: '⏳',
    [ProcessingStatus.PROCESSING]: '⚡',
    [ProcessingStatus.COMPLETED]: '✅',
    [ProcessingStatus.ERROR]: '❌',
    [ProcessingStatus.CANCELLED]: '⏹️'
  };

  badge.innerHTML = `
    <span class="mr-1">${statusIcons[status]}</span>
    ${statusText[status]}
  `;

  return badge;
}

/**
 * Creates a file size display
 */
export function createFileSizeDisplay(bytes: number): HTMLElement {
  const span = document.createElement('span');
  span.className = 'text-sm text-gray-500 dark:text-gray-400';
  span.textContent = formatFileSize(bytes);
  return span;
}

/**
 * Formats file size for display
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Creates a modal backdrop
 */
export function createModalBackdrop(onClose?: () => void): HTMLElement {
  const backdrop = document.createElement('div');
  backdrop.className = 'fixed inset-0 bg-black bg-opacity-50 z-40 flex items-center justify-center p-4';
  
  if (onClose) {
    backdrop.addEventListener('click', (e) => {
      if (e.target === backdrop) {
        onClose();
      }
    });
  }

  return backdrop;
}

/**
 * Creates a modal dialog
 */
export function createModal(title: string, content: HTMLElement, onClose?: () => void): HTMLElement {
  const backdrop = createModalBackdrop(onClose);
  
  const modal = document.createElement('div');
  modal.className = 'bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-hidden';
  
  modal.innerHTML = `
    <div class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
      <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">${title}</h3>
      <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" id="modal-close">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>
    <div class="p-4 overflow-y-auto"></div>
  `;

  const contentContainer = modal.querySelector('.p-4.overflow-y-auto') as HTMLElement;
  contentContainer.appendChild(content);

  const closeButton = modal.querySelector('#modal-close') as HTMLElement;
  if (closeButton && onClose) {
    closeButton.addEventListener('click', onClose);
  }

  backdrop.appendChild(modal);
  return backdrop;
}

/**
 * Debounce function for performance optimization
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func.apply(null, args), wait);
  };
}

/**
 * Throttle function for performance optimization
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func.apply(null, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * Animates a number change
 */
export function animateNumber(
  element: HTMLElement,
  from: number,
  to: number,
  duration: number = 1000,
  formatter?: (value: number) => string
): void {
  const startTime = Date.now();
  const difference = to - from;

  function update() {
    const elapsed = Date.now() - startTime;
    const progress = Math.min(elapsed / duration, 1);
    
    // Easing function (ease-out)
    const easeOut = 1 - Math.pow(1 - progress, 3);
    const current = from + (difference * easeOut);
    
    element.textContent = formatter ? formatter(current) : Math.round(current).toString();
    
    if (progress < 1) {
      requestAnimationFrame(update);
    }
  }

  requestAnimationFrame(update);
}

// Global toast manager instance
declare global {
  interface Window {
    toastManager: ToastManager;
  }
}

export const toastManager = new ToastManager();
window.toastManager = toastManager;
