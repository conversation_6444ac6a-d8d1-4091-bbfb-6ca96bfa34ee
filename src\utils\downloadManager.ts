import <PERSON><PERSON><PERSON><PERSON> from 'jszip';
import { saveAs } from 'file-saver';
import { ConvertedFile, DownloadOptions } from '../types';
import { DOWNLOAD_SETTINGS } from '../constants';
import { sanitizeFileName } from './fileUtils';
import { toastManager } from './uiUtils';

export class DownloadManager {
  private downloadedFiles = new Map<string, ConvertedFile>();

  /**
   * Downloads a single converted file
   */
  async downloadFile(convertedFile: ConvertedFile): Promise<void> {
    try {
      const fileName = sanitizeFileName(convertedFile.convertedName);
      saveAs(convertedFile.convertedBlob, fileName);
      
      this.downloadedFiles.set(convertedFile.id, convertedFile);
      toastManager.show(`Downloaded ${fileName}`, 'success');
    } catch (error) {
      console.error('Download failed:', error);
      toastManager.show('Download failed', 'error');
    }
  }

  /**
   * Downloads multiple files individually
   */
  async downloadFiles(convertedFiles: ConvertedFile[]): Promise<void> {
    if (convertedFiles.length === 0) {
      toastManager.show('No files to download', 'warning');
      return;
    }

    try {
      // Download files with a small delay to prevent browser blocking
      for (let i = 0; i < convertedFiles.length; i++) {
        const file = convertedFiles[i];
        if (file) {
          await this.downloadFile(file);
        }
        
        // Add delay between downloads to prevent browser blocking
        if (i < convertedFiles.length - 1) {
          await this.delay(500);
        }
      }

      toastManager.show(`Downloaded ${convertedFiles.length} files`, 'success');
    } catch (error) {
      console.error('Batch download failed:', error);
      toastManager.show('Some downloads may have failed', 'warning');
    }
  }

  /**
   * Downloads files as a ZIP archive
   */
  async downloadAsZip(
    convertedFiles: ConvertedFile[],
    zipFileName?: string
  ): Promise<void> {
    if (convertedFiles.length === 0) {
      toastManager.show('No files to download', 'warning');
      return;
    }

    try {
      toastManager.show('Creating ZIP archive...', 'info');
      
      const zip = new JSZip();
      const fileName = zipFileName || DOWNLOAD_SETTINGS.DEFAULT_ZIP_NAME;

      // Add files to ZIP
      for (const convertedFile of convertedFiles) {
        const sanitizedName = sanitizeFileName(convertedFile.convertedName);
        zip.file(sanitizedName, convertedFile.convertedBlob);
      }

      // Generate ZIP blob
      const zipBlob = await zip.generateAsync({
        type: 'blob',
        compression: 'DEFLATE',
        compressionOptions: {
          level: 6
        }
      });

      // Download ZIP
      saveAs(zipBlob, sanitizeFileName(fileName));
      
      // Track downloads
      convertedFiles.forEach(file => {
        this.downloadedFiles.set(file.id, file);
      });

      toastManager.show(`Downloaded ${convertedFiles.length} files as ZIP`, 'success');
    } catch (error) {
      console.error('ZIP download failed:', error);
      toastManager.show('Failed to create ZIP archive', 'error');
    }
  }

  /**
   * Downloads files based on options
   */
  async downloadWithOptions(
    convertedFiles: ConvertedFile[],
    options: DownloadOptions
  ): Promise<void> {
    if (options.bulk && options.individual) {
      // User wants both - ask for preference
      const userChoice = await this.showDownloadDialog(convertedFiles.length);
      if (userChoice === 'zip') {
        await this.downloadAsZip(convertedFiles, options.zipFileName);
      } else if (userChoice === 'individual') {
        await this.downloadFiles(convertedFiles);
      }
    } else if (options.bulk) {
      await this.downloadAsZip(convertedFiles, options.zipFileName);
    } else if (options.individual) {
      await this.downloadFiles(convertedFiles);
    } else {
      // Default to ZIP for multiple files, individual for single file
      if (convertedFiles.length > 1) {
        await this.downloadAsZip(convertedFiles, options.zipFileName);
      } else if (convertedFiles[0]) {
        await this.downloadFile(convertedFiles[0]);
      }
    }
  }

  /**
   * Shows a dialog to choose download method
   */
  private showDownloadDialog(fileCount: number): Promise<'zip' | 'individual' | 'cancel'> {
    return new Promise((resolve) => {
      const modal = document.createElement('div');
      modal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4';
      
      modal.innerHTML = `
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full p-6">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Choose Download Method</h3>
          <p class="text-sm text-gray-600 dark:text-gray-400 mb-6">
            How would you like to download ${fileCount} files?
          </p>
          <div class="space-y-3">
            <button id="download-zip" class="w-full btn-primary">
              Download as ZIP Archive
            </button>
            <button id="download-individual" class="w-full btn-secondary">
              Download Files Individually
            </button>
            <button id="download-cancel" class="w-full text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 py-2">
              Cancel
            </button>
          </div>
        </div>
      `;

      document.body.appendChild(modal);

      const cleanup = () => {
        document.body.removeChild(modal);
      };

      modal.querySelector('#download-zip')?.addEventListener('click', () => {
        cleanup();
        resolve('zip');
      });

      modal.querySelector('#download-individual')?.addEventListener('click', () => {
        cleanup();
        resolve('individual');
      });

      modal.querySelector('#download-cancel')?.addEventListener('click', () => {
        cleanup();
        resolve('cancel');
      });

      // Close on backdrop click
      modal.addEventListener('click', (e) => {
        if (e.target === modal) {
          cleanup();
          resolve('cancel');
        }
      });
    });
  }

  /**
   * Gets download statistics
   */
  getDownloadStats(): {
    totalDownloads: number;
    totalSize: number;
    downloadedFiles: ConvertedFile[];
  } {
    const downloadedFiles = Array.from(this.downloadedFiles.values());
    const totalSize = downloadedFiles.reduce((sum, file) => sum + file.convertedSize, 0);

    return {
      totalDownloads: downloadedFiles.length,
      totalSize,
      downloadedFiles
    };
  }

  /**
   * Checks if a file has been downloaded
   */
  isFileDownloaded(fileId: string): boolean {
    return this.downloadedFiles.has(fileId);
  }

  /**
   * Clears download history
   */
  clearDownloadHistory(): void {
    this.downloadedFiles.clear();
  }

  /**
   * Generates a unique ZIP filename
   */
  generateZipFileName(prefix: string = 'converted-images'): string {
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:.]/g, '-');
    return `${prefix}-${timestamp}.zip`;
  }

  /**
   * Estimates ZIP file size
   */
  estimateZipSize(convertedFiles: ConvertedFile[]): number {
    // ZIP compression typically reduces size by 10-30% for images
    const totalSize = convertedFiles.reduce((sum, file) => sum + file.convertedSize, 0);
    return Math.round(totalSize * 0.85); // Assume 15% compression
  }

  /**
   * Validates files before download
   */
  validateFilesForDownload(convertedFiles: ConvertedFile[]): {
    valid: ConvertedFile[];
    invalid: ConvertedFile[];
  } {
    const valid: ConvertedFile[] = [];
    const invalid: ConvertedFile[] = [];

    convertedFiles.forEach(file => {
      if (file.convertedBlob && file.convertedBlob.size > 0) {
        valid.push(file);
      } else {
        invalid.push(file);
      }
    });

    return { valid, invalid };
  }

  /**
   * Creates a download progress indicator
   */
  createDownloadProgress(fileName: string): {
    element: HTMLElement;
    update: (progress: number) => void;
    complete: () => void;
    error: () => void;
  } {
    const element = document.createElement('div');
    element.className = 'fixed bottom-4 right-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-4 max-w-sm z-50';
    
    element.innerHTML = `
      <div class="flex items-center space-x-3">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
            <svg class="w-4 h-4 text-primary-600 dark:text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
          </div>
        </div>
        <div class="flex-1 min-w-0">
          <p class="text-sm font-medium text-gray-900 dark:text-white truncate">Downloading ${fileName}</p>
          <div class="mt-1 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div id="progress-bar" class="bg-primary-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(element);

    const progressBar = element.querySelector('#progress-bar') as HTMLElement;

    return {
      element,
      update: (progress: number) => {
        if (progressBar) {
          progressBar.style.width = `${Math.max(0, Math.min(100, progress))}%`;
        }
      },
      complete: () => {
        element.classList.add('animate-fade-out');
        setTimeout(() => {
          if (element.parentNode) {
            element.parentNode.removeChild(element);
          }
        }, 300);
      },
      error: () => {
        element.classList.add('border-red-500');
        const icon = element.querySelector('svg');
        if (icon) {
          icon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>';
          icon.classList.remove('text-primary-600', 'dark:text-primary-400');
          icon.classList.add('text-red-600', 'dark:text-red-400');
        }
        setTimeout(() => {
          if (element.parentNode) {
            element.parentNode.removeChild(element);
          }
        }, 3000);
      }
    };
  }

  /**
   * Utility delay function
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Singleton instance
let downloadManagerInstance: DownloadManager | null = null;

export function getDownloadManager(): DownloadManager {
  if (!downloadManagerInstance) {
    downloadManagerInstance = new DownloadManager();
  }
  return downloadManagerInstance;
}
